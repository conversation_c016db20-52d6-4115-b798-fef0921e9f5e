@extends('layouts.admin')

@section('title', 'แก้ไขกิจกรรม - Admin Panel')

@php
use Illuminate\Support\Facades\Storage;

// ตรวจสอบว่ามีรูปภาพจริงๆ หรือไม่
$hasImage = false;
$imageUrl = asset('images/no-image.svg');
$actualImagePath = null;

if ($activity->cover_image) {
    $imagePath = 'activities/' . $activity->cover_image;
    $imageExists = Storage::disk('public')->exists($imagePath);

    // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
    if (!$imageExists) {
        $files = Storage::disk('public')->files('activities');
        $baseName = pathinfo($activity->cover_image, PATHINFO_FILENAME);

        foreach ($files as $file) {
            $fileName = basename($file);
            if (strpos($fileName, $baseName) !== false) {
                $imagePath = $file;
                $imageExists = true;
                break;
            }
        }
    }

    if ($imageExists) {
        $hasImage = true;
        $imageUrl = url('storage/' . $imagePath);
        $actualImagePath = $imagePath;
    }
}
@endphp

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-warning"></i>แก้ไขกิจกรรม
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลกิจกรรม: {{ $activity->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.index') }}">
                                <i class="fas fa-images"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม: {{ $activity->title }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data" id="activityForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="remove_cover_image" id="removeImageFlag" value="0">
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $activity->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description', $activity->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category_id" class="form-label">หมวดหมู่</label>
                                                    <select class="form-control @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                                        <option value="">เลือกหมวดหมู่</option>
                                                        @foreach($categories ?? [] as $category)
                                                            <option value="{{ $category->id }}" 
                                                                    {{ old('category_id', $activity->category_id) == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('category_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                                    <input type="date" class="form-control @error('activity_date') is-invalid @enderror" 
                                                           id="activity_date" name="activity_date" 
                                                           value="{{ old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1" 
                                                       {{ old('is_published', $activity->is_published) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรมทันที
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                            <input type="file" class="form-control @error('cover_image') is-invalid @enderror"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('cover_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>



                                        <div class="text-center">
                                            <label class="form-label">รูปภาพปัจจุบัน</label>

                                            @if($hasImage)
                                                <div id="imagePreview" style="display: block;">
                                                    <img id="previewImg" src="{{ $imageUrl }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="รูปภาพกิจกรรม"
                                                         onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                    <div class="mt-2">
                                                        <small class="text-muted d-block mb-2">
                                                            <i class="fas fa-file-image me-1"></i>
                                                            ไฟล์: {{ $activity->cover_image }}
                                                        </small>
                                                        @if($actualImagePath)
                                                            <small class="text-success d-block mb-2">
                                                                <i class="fas fa-check-circle me-1"></i>
                                                                พาธ: {{ $actualImagePath }}
                                                            </small>
                                                        @endif
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                <div id="imagePreview" style="display: none;">
                                                    <img id="previewImg" src="{{ asset('images/no-image.svg') }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="ไม่มีรูปภาพ">
                                                    <div class="mt-2">
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>

                                                <div id="noImageDisplay" class="border border-dashed rounded p-4 text-muted">
                                                    <i class="fas fa-image fa-3x mb-2"></i>
                                                    <p class="mb-1">ไม่มีรูปภาพ</p>
                                                    @if($activity->cover_image)
                                                        <small class="text-warning">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                                            ไฟล์ {{ $activity->cover_image }} ไม่พบ
                                                        </small>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('activities.show', $activity) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตกิจกรรม
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Tabbed Interface -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-white border-0">
                            <ul class="nav nav-tabs card-header-tabs" id="activityTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="cover-tab" data-bs-toggle="tab" data-bs-target="#cover" type="button" role="tab">
                                        <i class="fas fa-image"></i> รูปหน้าปก
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="gallery-tab" data-bs-toggle="tab" data-bs-target="#gallery" type="button" role="tab">
                                        <i class="fas fa-images"></i> แกลเลอรี่ ({{ $activity->images->count() }} รูป)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                                        <i class="fas fa-cog"></i> การตั้งค่า
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="activityTabsContent">
                                <!-- Cover Image Tab -->
                                <div class="tab-pane fade show active" id="cover" role="tabpanel">
                                    <h5 class="mb-3">จัดการรูปภาพหน้าปก</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data" id="coverForm">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="cover_only" value="1">
                                                <input type="hidden" name="remove_cover_image" id="removeCoverFlag" value="0">

                                                <div class="mb-3">
                                                    <label for="new_cover_image" class="form-label">เปลี่ยนรูปหน้าปก</label>
                                                    <input type="file" class="form-control" id="new_cover_image" name="cover_image" accept="image/*">
                                                    <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                                </div>

                                                <div class="d-flex gap-2">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-save"></i> บันทึกรูปหน้าปก
                                                    </button>
                                                    @if($hasImage)
                                                        <button type="button" class="btn btn-danger" id="removeCoverBtn">
                                                            <i class="fas fa-trash"></i> ลบรูปหน้าปก
                                                        </button>
                                                    @endif
                                                </div>
                                            </form>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">รูปภาพปัจจุบัน</label>
                                            <div id="coverPreview">
                                                @if($hasImage)
                                                    <img id="coverImg" src="{{ $imageUrl }}" class="img-thumbnail w-100"
                                                         style="max-height: 300px; object-fit: cover;" alt="รูปหน้าปก">
                                                    <div class="mt-2">
                                                        <small class="text-muted d-block">
                                                            <i class="fas fa-file-image me-1"></i>
                                                            ไฟล์: {{ $activity->cover_image }}
                                                        </small>
                                                    </div>
                                                @else
                                                    <div class="border border-dashed rounded p-4 text-center text-muted">
                                                        <i class="fas fa-image fa-3x mb-2"></i>
                                                        <p class="mb-0">ไม่มีรูปหน้าปก</p>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gallery Tab -->
                                <div class="tab-pane fade" id="gallery" role="tabpanel">
                                    <h5 class="mb-3">จัดการแกลเลอรี่ภาพ</h5>

                                    <!-- Upload New Images -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">
                                                        <i class="fas fa-upload text-success"></i> เพิ่มรูปภาพใหม่
                                                    </h6>
                                                    <form action="{{ route('admin.activities.gallery.upload', $activity) }}" method="POST" enctype="multipart/form-data" id="galleryUploadForm">
                                                        @csrf

                                                        <div class="mb-3">
                                                            <input type="file" class="form-control" id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                                            <small class="form-text text-muted">เลือกหลายไฟล์พร้อมกัน (JPG, PNG, GIF - ขนาดไม่เกิน 2MB ต่อไฟล์)</small>
                                                        </div>

                                                        <div id="galleryPreview" class="row mb-3">
                                                            <!-- Preview images will appear here -->
                                                        </div>

                                                        <div id="galleryHelp" class="text-center text-muted mb-3">
                                                            <i class="fas fa-images fa-2x mb-2"></i>
                                                            <p class="mb-0">เลือกรูปภาพเพื่อดู preview ก่อนอัพโหลด</p>
                                                        </div>

                                                        <div class="text-center">
                                                            <button type="submit" class="btn btn-success" id="galleryUploadBtn" style="display: none;">
                                                                <i class="fas fa-upload"></i> อัพโหลดรูปภาพ (<span id="galleryFileCount">0</span> ไฟล์)
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Current Gallery Images -->
                                    @if($activity->images && $activity->images->count() > 0)
                                        <div class="row" id="currentGallery">
                                            @foreach($activity->images as $image)
                                                <div class="col-lg-2 col-md-3 col-4 mb-3" data-image-id="{{ $image->id }}">
                                                    <div class="gallery-item-card border rounded p-2 bg-light position-relative">
                                                        <!-- Small Preview Image -->
                                                        <div class="text-center mb-2">
                                                            <img src="{{ asset('storage/activities/gallery/' . $image->image_path) }}"
                                                                 class="img-thumbnail gallery-thumb"
                                                                 style="width: 80px; height: 80px; object-fit: cover; cursor: pointer;"
                                                                 alt="รูปที่ {{ $image->sort_order }}"
                                                                 onclick="viewImageLarge(this.src, '{{ $image->image_path }}')"
                                                                 title="คลิกเพื่อดูรูปใหญ่">
                                                        </div>

                                                        <!-- Image Info -->
                                                        <div class="text-center">
                                                            <small class="d-block text-muted mb-1">
                                                                <strong>รูปที่ {{ $image->sort_order }}</strong>
                                                            </small>
                                                            <small class="d-block text-muted mb-2" style="font-size: 10px;">
                                                                {{ $image->image_path }}
                                                            </small>
                                                        </div>

                                                        <!-- Action Buttons -->
                                                        <div class="d-flex justify-content-center gap-1">
                                                            <button type="button" class="btn btn-info btn-sm"
                                                                    onclick="viewImageLarge('{{ asset('storage/activities/gallery/' . $image->image_path) }}', '{{ $image->image_path }}')"
                                                                    title="ดูรูปใหญ่">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-danger btn-sm"
                                                                    onclick="confirmDeleteImage({{ $image->id }}, '{{ $image->image_path }}')"
                                                                    title="ลบรูปภาพนี้">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="text-center text-muted py-5" id="emptyGallery">
                                            <i class="fas fa-images fa-4x mb-3"></i>
                                            <h5>ยังไม่มีรูปภาพในแกลเลอรี่</h5>
                                            <p class="mb-0">เริ่มต้นด้วยการอัพโหลดรูปภาพแรกของคุณ</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- Settings Tab -->
                                <div class="tab-pane fade" id="settings" role="tabpanel">
                                    <h5 class="mb-3">การตั้งค่าและการจัดการ</h5>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border-info">
                                                <div class="card-header bg-info text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="text-muted">ดูกิจกรรมนี้ในหน้าเว็บไซต์</p>
                                                    <a href="{{ route('activities.show', $activity) }}" target="_blank" class="btn btn-info">
                                                        <i class="fas fa-external-link-alt"></i> เปิดหน้าบ้าน
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="card border-danger">
                                                <div class="card-header bg-danger text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-trash"></i> ลบกิจกรรม
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="text-muted">การลบกิจกรรมจะไม่สามารถกู้คืนได้</p>
                                                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                                        <i class="fas fa-trash"></i> ลบกิจกรรมนี้
                                                    </button>
                                                    <form action="{{ route('admin.activities.destroy', $activity) }}" method="POST" id="deleteForm" style="display: none;">
                                                        @csrf
                                                        @method('DELETE')
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25);
}

/* Gallery Item Cards */
.gallery-item-card {
    transition: all 0.3s ease;
    background: #f8f9fa !important;
}
.gallery-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    background: #fff !important;
}

.gallery-thumb {
    transition: transform 0.2s ease;
    border: 2px solid #dee2e6;
}
.gallery-thumb:hover {
    transform: scale(1.05);
    border-color: #007bff;
}

/* Tab styling */
.nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
}
.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Button improvements */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Preview items */
.gallery-preview-item {
    transition: all 0.3s ease;
}
.gallery-preview-item:hover {
    transform: scale(1.02);
}
</style>
@endpush

@push('scripts')
<script>
// Ensure jQuery and SweetAlert2 are loaded
if (typeof $ === 'undefined') {
    console.error('jQuery is not loaded!');
}
if (typeof Swal === 'undefined') {
    console.error('SweetAlert2 is not loaded!');
}

$(document).ready(function() {
    console.log('Activities edit page loaded'); // Debug
    // ตรวจสอบว่ามีรูปจริงๆ หรือไม่
    const hasImage = {{ $hasImage ? 'true' : 'false' }};
    const originalImageUrl = "{{ $imageUrl }}";
    const originalImageName = "{{ $activity->cover_image ?? '' }}";

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Cover image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            $('#removeImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $(document).on('click', '#removeImage', function() {
        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $('#removeImageFlag').val('1');
                $('#cover_image').val('');
                $('#imagePreview').hide();
                $('#noImageDisplay').show();

                Swal.fire({
                    icon: 'success',
                    title: 'ลบรูปภาพแล้ว',
                    text: 'รูปภาพจะถูกลบเมื่อกดบันทึก',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Form submission with stay on page option
    $('#activityForm').on('submit', function(e) {
        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...').prop('disabled', true);

        // Re-enable button after 3 seconds (fallback)
        setTimeout(function() {
            submitBtn.html(originalText).prop('disabled', false);
        }, 3000);
    });

    // Cover image preview
    $('#new_cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'ไฟล์ไม่ถูกต้อง',
                    text: 'กรุณาเลือกไฟล์รูปภาพเท่านั้น'
                });
                this.value = '';
                return;
            }

            // Validate file size (2MB)
            if (file.size > 2 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'ไฟล์ใหญ่เกินไป',
                    text: 'กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB'
                });
                this.value = '';
                return;
            }

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#coverImg').attr('src', e.target.result);
                $('#coverPreview').html(`
                    <img id="coverImg" src="${e.target.result}" class="img-thumbnail w-100"
                         style="max-height: 300px; object-fit: cover;" alt="รูปหน้าปก">
                    <div class="mt-2">
                        <small class="text-success d-block">
                            <i class="fas fa-check-circle me-1"></i>
                            รูปภาพใหม่พร้อมบันทึก
                        </small>
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove cover image
    $('#removeCoverBtn').on('click', function() {
        Swal.fire({
            title: 'ลบรูปหน้าปก?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปหน้าปก?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $('#removeCoverFlag').val('1');
                $('#coverForm').submit();
            }
        });
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        const files = this.files;
        const previewContainer = $('#galleryPreview');
        const uploadBtn = $('#galleryUploadBtn');
        const uploadHelp = $('#galleryHelp');

        previewContainer.empty();

        if (files.length > 0) {
            uploadBtn.show();
            uploadHelp.hide();
            $('#galleryFileCount').text(files.length);

            Array.from(files).forEach((file, index) => {
                if (!file.type.startsWith('image/')) {
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageHtml = `
                        <div class="col-lg-2 col-md-3 col-4 mb-2">
                            <div class="position-relative gallery-preview-item">
                                <img src="${e.target.result}" class="img-thumbnail w-100"
                                     style="height: 120px; object-fit: cover; border-radius: 8px;"
                                     alt="Preview">
                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                        style="padding: 2px 6px; font-size: 10px; border-radius: 50%;"
                                        onclick="removeGalleryPreviewItem(this, ${index})"
                                        title="ลบรูปภาพนี้">
                                    <i class="fas fa-times"></i>
                                </button>
                                <div class="position-absolute bottom-0 start-0 m-1">
                                    <small class="badge bg-primary">${index + 1}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewContainer.append(imageHtml);
                };
                reader.readAsDataURL(file);
            });
        } else {
            uploadBtn.hide();
            uploadHelp.show();
            $('#galleryFileCount').text(0);
        }
    });

    // Update gallery count in tab
    function updateGalleryTabCount() {
        const currentCount = $('#currentGallery [data-image-id]').length;
        $('#gallery-tab').html(`<i class="fas fa-images"></i> แกลเลอรี่ (${currentCount} รูป)`);
    }
});

function removeGalleryPreviewItem(button, index) {
    $(button).closest('.col-lg-2').remove();

    // Update file count
    const remainingItems = $('#galleryPreview .col-lg-2').length;
    $('#galleryFileCount').text(remainingItems);

    if (remainingItems === 0) {
        $('#galleryUploadBtn').hide();
        $('#galleryHelp').show();
    }
}

function confirmDeleteImage(imageId, imageName) {
    Swal.fire({
        title: 'ลบรูปภาพ?',
        html: `
            <div class="text-center mb-3">
                <img src="{{ asset('storage/activities/gallery/') }}/${imageName}"
                     style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 8px;"
                     alt="รูปที่จะลบ">
            </div>
            <p>คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?</p>
            <small class="text-muted">ไฟล์: ${imageName}</small>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
        cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
        reverseButtons: true,
        width: '400px'
    }).then((result) => {
        if (result.isConfirmed) {
            deleteGalleryImage(imageId);
        }
    });
}

function deleteGalleryImage(imageId) {
    // Show loading
    Swal.fire({
        title: 'กำลังลบรูปภาพ...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: `{{ url('admin/activities/' . $activity->id . '/images') }}/${imageId}`,
        type: 'DELETE',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                $(`[data-image-id="${imageId}"]`).fadeOut(function() {
                    $(this).remove();
                    updateGalleryTabCount();

                    // Check if gallery is empty
                    if ($('#currentGallery [data-image-id]').length === 0) {
                        $('#currentGallery').hide();
                        $('#emptyGallery').show();
                    }
                });
                Swal.fire({
                    icon: 'success',
                    title: 'ลบสำเร็จ!',
                    text: 'ลบรูปภาพเรียบร้อยแล้ว',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด!',
                    text: response.message || 'ไม่สามารถลบรูปภาพได้'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาด!',
                text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้'
            });
        }
    });
}

function viewImageLarge(src, imageName) {
    Swal.fire({
        title: `<small class="text-muted">ไฟล์: ${imageName || 'รูปภาพ'}</small>`,
        imageUrl: src,
        imageAlt: 'Gallery Image',
        showCloseButton: true,
        showConfirmButton: false,
        width: 'auto',
        padding: '1rem',
        background: '#fff',
        customClass: {
            image: 'img-fluid',
            title: 'text-start'
        },
        imageWidth: 'auto',
        imageHeight: 'auto',
        backdrop: 'rgba(0,0,0,0.8)'
    });
}

// Keep old function for compatibility
function viewImage(src) {
    viewImageLarge(src, '');
}

function confirmDelete() {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
        cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('deleteForm').submit();
        }
    });
}
</script>
@endpush
