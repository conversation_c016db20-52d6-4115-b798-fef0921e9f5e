@extends('layouts.admin')

@section('title', 'แก้ไขกิจกรรม - Admin Panel')

@php
use Illuminate\Support\Facades\Storage;

// ตรวจสอบว่ามีรูปภาพจริงๆ หรือไม่
$hasImage = false;
$imageUrl = asset('images/no-image.svg');
$actualImagePath = null;

if ($activity->cover_image) {
    $imagePath = 'activities/' . $activity->cover_image;
    $imageExists = Storage::disk('public')->exists($imagePath);

    // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
    if (!$imageExists) {
        $files = Storage::disk('public')->files('activities');
        $baseName = pathinfo($activity->cover_image, PATHINFO_FILENAME);

        foreach ($files as $file) {
            $fileName = basename($file);
            if (strpos($fileName, $baseName) !== false) {
                $imagePath = $file;
                $imageExists = true;
                break;
            }
        }
    }

    if ($imageExists) {
        $hasImage = true;
        $imageUrl = url('storage/' . $imagePath);
        $actualImagePath = $imagePath;
    }
}
@endphp

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-warning"></i>แก้ไขกิจกรรม
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลกิจกรรม: {{ $activity->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activities.index') }}">
                                <i class="fas fa-images"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม: {{ $activity->title }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data" id="activityForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="remove_cover_image" id="removeImageFlag" value="0">
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $activity->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description', $activity->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category_id" class="form-label">หมวดหมู่</label>
                                                    <select class="form-control @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                                        <option value="">เลือกหมวดหมู่</option>
                                                        @foreach($categories ?? [] as $category)
                                                            <option value="{{ $category->id }}" 
                                                                    {{ old('category_id', $activity->category_id) == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('category_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                                    <input type="date" class="form-control @error('activity_date') is-invalid @enderror" 
                                                           id="activity_date" name="activity_date" 
                                                           value="{{ old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1" 
                                                       {{ old('is_published', $activity->is_published) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรมทันที
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                            <input type="file" class="form-control @error('cover_image') is-invalid @enderror"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('cover_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>



                                        <div class="text-center">
                                            <label class="form-label">รูปภาพปัจจุบัน</label>

                                            @if($hasImage)
                                                <div id="imagePreview" style="display: block;">
                                                    <img id="previewImg" src="{{ $imageUrl }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="รูปภาพกิจกรรม"
                                                         onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                    <div class="mt-2">
                                                        <small class="text-muted d-block mb-2">
                                                            <i class="fas fa-file-image me-1"></i>
                                                            ไฟล์: {{ $activity->cover_image }}
                                                        </small>
                                                        @if($actualImagePath)
                                                            <small class="text-success d-block mb-2">
                                                                <i class="fas fa-check-circle me-1"></i>
                                                                พาธ: {{ $actualImagePath }}
                                                            </small>
                                                        @endif
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                <div id="imagePreview" style="display: none;">
                                                    <img id="previewImg" src="{{ asset('images/no-image.svg') }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="ไม่มีรูปภาพ">
                                                    <div class="mt-2">
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>

                                                <div id="noImageDisplay" class="border border-dashed rounded p-4 text-muted">
                                                    <i class="fas fa-image fa-3x mb-2"></i>
                                                    <p class="mb-1">ไม่มีรูปภาพ</p>
                                                    @if($activity->cover_image)
                                                        <small class="text-warning">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                                            ไฟล์ {{ $activity->cover_image }} ไม่พบ
                                                        </small>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('activities.show', $activity) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตกิจกรรม
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Gallery Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-images"></i> แกลเลอรี่ภาพ ({{ $activity->images->count() }} รูป)
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Current Gallery Images -->
                            @if($activity->images && $activity->images->count() > 0)
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2">รูปภาพปัจจุบัน:</h6>
                                    <div class="row">
                                        @foreach($activity->images as $image)
                                            <div class="col-md-3 col-6 mb-2">
                                                <div class="gallery-item position-relative" data-image-id="{{ $image->id }}">
                                                    <img src="{{ asset('storage/activities/gallery/' . $image->image_path) }}" 
                                                         class="img-thumbnail w-100" style="height: 100px; object-fit: cover; cursor: pointer;"
                                                         alt="Gallery Image" onclick="viewImage(this.src)">
                                                    <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" 
                                                            style="padding: 2px 6px; font-size: 10px;"
                                                            onclick="deleteGalleryImage({{ $image->id }})" 
                                                            data-confirm="คุณต้องการลบรูปภาพนี้หรือไม่?">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <hr>
                            @endif

                            <!-- Add New Images -->
                            <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data" id="galleryForm">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="gallery_only" value="1">
                                
                                <div class="mb-3">
                                    <label for="gallery_images" class="form-label">เพิ่มรูปภาพใหม่</label>
                                    <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                           id="gallery_images" name="gallery_images[]" accept="image/*" multiple
                                           onchange="console.log('File input changed:', this.files.length)">
                                    <small class="form-text text-muted">สามารถเลือกหลายไฟล์พร้อมกัน (JPG, PNG, GIF - ขนาดไม่เกิน 2MB ต่อไฟล์)</small>
                                    @error('gallery_images')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div id="newGalleryPreview" class="row mb-3">
                                    <!-- Preview images will appear here -->
                                </div>

                                <div id="galleryHelp" class="text-center text-muted mb-3">
                                    <i class="fas fa-images fa-2x mb-2"></i>
                                    <p class="mb-0">เลือกรูปภาพเพื่อดู preview ก่อนอัพโหลด</p>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-success" id="uploadGalleryBtn" style="display: none;">
                                        <i class="fas fa-upload"></i> อัพโหลดรูปภาพ (<span id="fileCount">0</span> ไฟล์)
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบกิจกรรม
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">การลบกิจกรรมจะไม่สามารถกู้คืนได้ กรุณาตรวจสอบให้แน่ใจก่อนดำเนินการ</p>
                            <form action="{{ route('admin.activities.destroy', $activity) }}" method="POST" id="deleteForm">
                                @csrf
                                @method('DELETE')
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash"></i> ลบกิจกรรมนี้
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
// Ensure jQuery and SweetAlert2 are loaded
if (typeof $ === 'undefined') {
    console.error('jQuery is not loaded!');
}
if (typeof Swal === 'undefined') {
    console.error('SweetAlert2 is not loaded!');
}

$(document).ready(function() {
    console.log('Activities edit page loaded'); // Debug
    // ตรวจสอบว่ามีรูปจริงๆ หรือไม่
    const hasImage = {{ $hasImage ? 'true' : 'false' }};
    const originalImageUrl = "{{ $imageUrl }}";
    const originalImageName = "{{ $activity->cover_image ?? '' }}";

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Cover image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            $('#removeImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $(document).on('click', '#removeImage', function() {
        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $('#removeImageFlag').val('1');
                $('#cover_image').val('');
                $('#imagePreview').hide();
                $('#noImageDisplay').show();

                Swal.fire({
                    icon: 'success',
                    title: 'ลบรูปภาพแล้ว',
                    text: 'รูปภาพจะถูกลบเมื่อกดบันทึก',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        const files = this.files;
        const previewContainer = $('#newGalleryPreview');
        const uploadBtn = $('#uploadGalleryBtn');

        console.log('Gallery files selected:', files.length); // Debug

        previewContainer.empty();

        if (files.length > 0) {
            uploadBtn.show();
            $('#galleryHelp').hide();
            $('#fileCount').text(files.length);

            Array.from(files).forEach((file, index) => {
                // Validate file type
                if (!file.type.startsWith('image/')) {
                    console.log('Skipping non-image file:', file.name);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageHtml = `
                        <div class="col-md-3 col-6 mb-2">
                            <div class="position-relative gallery-preview-item" data-new-index="${index}">
                                <img src="${e.target.result}" class="img-thumbnail w-100"
                                     style="height: 100px; object-fit: cover; border-radius: 8px;"
                                     alt="New Gallery Preview">
                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                        style="padding: 2px 6px; font-size: 10px; border-radius: 50%;"
                                        onclick="removeNewGalleryItem(${index})"
                                        title="ลบรูปภาพนี้">
                                    <i class="fas fa-times"></i>
                                </button>
                                <div class="position-absolute bottom-0 start-0 m-1">
                                    <small class="badge bg-primary">${index + 1}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewContainer.append(imageHtml);
                };
                reader.onerror = function() {
                    console.error('Error reading file:', file.name);
                };
                reader.readAsDataURL(file);
            });
        } else {
            uploadBtn.hide();
            $('#galleryHelp').show();
            $('#fileCount').text(0);
        }
    });

    // Fallback: Add vanilla JavaScript event listener
    const galleryInput = document.getElementById('gallery_images');
    if (galleryInput) {
        galleryInput.addEventListener('change', function(e) {
            console.log('Vanilla JS: Gallery input changed', e.target.files.length);
            // This will trigger the jQuery event above
        });
    }
});

function deleteGalleryImage(imageId) {
    Swal.fire({
        title: 'ลบรูปภาพ?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
        cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ url('admin/activities/' . $activity->id . '/images') }}/${imageId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        $(`.gallery-item[data-image-id="${imageId}"]`).closest('.col-md-3').fadeOut(function() {
                            $(this).remove();
                            updateGalleryCount();
                        });
                        Swal.fire({
                            icon: 'success',
                            title: 'ลบสำเร็จ!',
                            text: 'ลบรูปภาพเรียบร้อยแล้ว',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'เกิดข้อผิดพลาด!',
                            text: response.message || 'ไม่สามารถลบรูปภาพได้'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'เกิดข้อผิดพลาด!',
                        text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้'
                    });
                }
            });
        }
    });
}

function removeNewGalleryItem(index) {
    console.log('Removing gallery item:', index); // Debug

    // Remove the preview element
    $(`.gallery-preview-item[data-new-index="${index}"]`).closest('.col-md-3').fadeOut(300, function() {
        $(this).remove();

        // Update numbering for remaining items
        $('.gallery-preview-item').each(function(i) {
            $(this).find('.badge').text(i + 1);
        });
    });

    // Update the file input
    const input = document.getElementById('gallery_images');
    if (input && input.files) {
        try {
            const dt = new DataTransfer();
            const files = Array.from(input.files);

            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });

            input.files = dt.files;

            // Update UI based on remaining files
            if (input.files.length === 0) {
                $('#uploadGalleryBtn').hide();
                $('#newGalleryPreview').empty();
                $('#galleryHelp').show();
                $('#fileCount').text(0);
            } else {
                $('#fileCount').text(input.files.length);
            }

            console.log('Files remaining:', input.files.length); // Debug
        } catch (error) {
            console.error('Error updating file input:', error);
            // Fallback: just clear the input
            input.value = '';
            $('#uploadGalleryBtn').hide();
            $('#newGalleryPreview').empty();
        }
    }
}

function updateGalleryCount() {
    const currentCount = $('.gallery-item[data-image-id]').length;
    $('.card-header h5').html(`<i class="fas fa-images"></i> แกลเลอรี่ภาพ (${currentCount} รูป)`);
}

function viewImage(src) {
    Swal.fire({
        imageUrl: src,
        imageAlt: 'Gallery Image',
        showCloseButton: true,
        showConfirmButton: false,
        width: 'auto',
        padding: '1rem',
        background: '#fff',
        customClass: {
            image: 'img-fluid'
        }
    });
}

function confirmDelete() {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
        cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('deleteForm').submit();
        }
    });
}
</script>
@endpush
