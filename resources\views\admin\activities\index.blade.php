@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม - Admin Panel')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('styles')
<style>
    .activity-card {
        transition: all 0.3s ease;
        user-select: none;
    }
    .activity-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        border: 2px solid #007bff;
    }
    .activity-card:active {
        transform: translateY(-2px);
    }
    .action-bar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .activity-card .card-body {
        pointer-events: none;
    }
    .activity-card .card-body * {
        pointer-events: none;
    }
    .activity-card .form-check,
    .activity-card .dropdown {
        pointer-events: auto;
    }
    .activity-card .form-check *,
    .activity-card .dropdown * {
        pointer-events: auto;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>จัดการกิจกรรม
                    </h1>
                    <p class="text-muted">จัดการกิจกรรมทั้งหมดของเว็บไซต์</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <a href="{{ route('admin.activities.create') }}" class="btn btn-primary me-2">
                                        <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                                    </a>
                                    <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> ลบที่เลือก
                                    </button>
                                    <span class="text-muted" id="selectedCount"></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหากิจกรรม...">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activities Grid -->
            <div class="row" id="activitiesGrid">
                @forelse($activities as $activity)
                    <div class="col-lg-4 col-md-6 col-12 mb-4 activity-item" data-activity-id="{{ $activity->id }}">
                        <div class="card shadow-sm border-0 h-100 activity-card"
                             ondblclick="window.location.href='{{ route('admin.activities.edit', $activity) }}'"
                             style="cursor: pointer;">
                            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input activity-checkbox" type="checkbox" value="{{ $activity->id }}"
                                           onclick="event.stopPropagation();">
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                            onclick="event.stopPropagation();">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ route('admin.activities.edit', $activity) }}">
                                                <i class="fas fa-edit text-primary"></i> แก้ไข
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item view-activity" href="{{ route('activities.show', $activity) }}" target="_blank">
                                                <i class="fas fa-eye text-info"></i> ดูหน้าบ้าน
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item delete-activity" href="#" data-activity-id="{{ $activity->id }}">
                                                <i class="fas fa-trash text-danger"></i> ลบ
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>


                            <div class="position-relative">
                                @php
                                    // ตรวจสอบไฟล์ที่มีอยู่จริง
                                    $imagePath = $activity->cover_image ? 'activities/' . $activity->cover_image : null;
                                    $imageExists = $imagePath && Storage::disk('public')->exists($imagePath);

                                    // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
                                    if (!$imageExists && $activity->cover_image) {
                                        $files = Storage::disk('public')->files('activities');
                                        $baseName = pathinfo($activity->cover_image, PATHINFO_FILENAME);

                                        foreach ($files as $file) {
                                            $fileName = basename($file);
                                            if (strpos($fileName, $baseName) !== false) {
                                                $imagePath = $file;
                                                $imageExists = true;
                                                break;
                                            }
                                        }
                                    }

                                    $imageUrl = $imageExists ? url('storage/' . $imagePath) : asset('images/no-image.svg');
                                @endphp
                                <img src="{{ $imageUrl }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ $activity->title }}"
                                     onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                <div class="position-absolute top-0 end-0 m-2">
                                    @if($activity->is_published)
                                        <span class="badge bg-success">เผยแพร่แล้ว</span>
                                    @else
                                        <span class="badge bg-warning">ร่าง</span>
                                    @endif
                                </div>
                            </div>

                            <div class="card-body">
                                <h5 class="card-title">{{ $activity->title }}</h5>
                                <p class="card-text text-muted">{{ Str::limit($activity->description, 100) }}</p>

                                @if($activity->category)
                                    <div class="mb-2">
                                        <span class="badge" style="background-color: {{ $activity->category->color }}">
                                            {{ $activity->category->name }}
                                        </span>
                                    </div>
                                @endif

                                @if($activity->images->count() > 0)
                                    <div class="mb-2">
                                        <small class="text-info">
                                            <i class="fas fa-images"></i> {{ $activity->images->count() }} รูป
                                        </small>
                                    </div>
                                @endif

                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        @if($activity->activity_date)
                                            {{ $activity->activity_date->format('d/m/Y') }}
                                        @else
                                            {{ $activity->created_at->format('d/m/Y') }}
                                        @endif
                                    </small>
                                    <div>
                                        <small class="text-muted">
                                            <i class="fas fa-mouse-pointer"></i> ดับเบิลคลิกเพื่อแก้ไข
                                        </small>
                                    </div>
                                </div>
                            </div>
                </div>
            </div>
                @empty
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                                <p class="text-muted">เริ่มต้นด้วยการเพิ่มกิจกรรมแรกของคุณ</p>
                                <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                                </a>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
</div>



@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize
    updateSelectedCount();

    // Search functionality
    $('#searchInput').on('input', function() {
        let searchTerm = $(this).val().toLowerCase();
        filterActivities(searchTerm);
    });

    $('#searchBtn').on('click', function() {
        let searchTerm = $('#searchInput').val().toLowerCase();
        filterActivities(searchTerm);
    });

    // Checkbox functionality
    $(document).on('change', '.activity-checkbox', function() {
        updateSelectedCount();
    });

    // Bulk delete functionality
    $('#bulkDeleteBtn').on('click', function() {
        let selectedIds = [];
        $('.activity-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            Swal.fire('แจ้งเตือน', 'กรุณาเลือกกิจกรรมที่ต้องการลบ', 'warning');
            return;
        }

        Swal.fire({
            title: 'ลบกิจกรรมที่เลือก?',
            text: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม ${selectedIds.length} รายการ? การดำเนินการนี้ไม่สามารถยกเลิกได้`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'ลบ',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                bulkDeleteActivities(selectedIds);
            }
        });
    });

    // Delete activity functionality
    $(document).on('click', '.delete-activity', function(e) {
        e.preventDefault();
        let activityId = $(this).data('activity-id');
        deleteActivity(activityId);
    });
});

function updateSelectedCount() {
    let selectedCount = $('.activity-checkbox:checked').length;

    if (selectedCount > 0) {
        $('#selectedCount').text(`${selectedCount} รายการที่เลือก`);
        $('#bulkDeleteBtn').show();
    } else {
        $('#selectedCount').text('');
        $('#bulkDeleteBtn').hide();
    }
}

function filterActivities(searchTerm) {
    $('.activity-item').each(function() {
        let activityTitle = $(this).find('.card-title').text().toLowerCase();
        let activityDesc = $(this).find('.card-text').text().toLowerCase();

        if (activityTitle.includes(searchTerm) || activityDesc.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}



function deleteActivity(activityId) {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'ลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ url('admin/activities') }}/${activityId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ!', 'ลบกิจกรรมเรียบร้อยแล้ว', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด!', response.message || 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                    }
                },
                error: function(xhr) {
                    let message = 'เกิดข้อผิดพลาดในการลบกิจกรรม';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Swal.fire('ข้อผิดพลาด!', message, 'error');
                }
            });
        }
    });
}

function bulkDeleteActivities(selectedIds) {
    $.ajax({
        url: '{{ route("admin.activities.bulk-delete") }}',
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            ids: selectedIds
        },
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ!', `ลบกิจกรรม ${selectedIds.length} รายการเรียบร้อยแล้ว`, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด!', response.message || 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
            }
        },
        error: function(xhr) {
            let message = 'เกิดข้อผิดพลาดในการลบกิจกรรม';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            Swal.fire('ข้อผิดพลาด!', message, 'error');
        }
    });
}
</script>
@endsection
